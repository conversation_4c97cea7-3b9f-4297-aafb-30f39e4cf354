package main

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	_ "net/http/pprof"
	"os"
	"strings"
	"sync"
	tu "tradeup_calc/tradeup"
	"tradeup_calc/types"
)

var pow10 = [...]float64{1, 10, 100, 1000, 10000, 100000, 1000000}

func roundFloat(val float64, precision uint) float64 {
	if precision < uint(len(pow10)) {
		ratio := pow10[precision]
		return math.Round(val*ratio) / ratio
	}
	ratio := math.Pow(10, float64(precision))
	return math.Round(val*ratio) / ratio
}

func main() {
	go func() {
		fmt.Println("pprof listening on :6060")
		http.ListenAndServe("localhost:6060", nil)
	}()

	floats := []float64{0.06, 0.11, 0.26, 0.41, 0.60}
	combinationsToCheck := [][2]int{{9, 1}, {8, 2}, {7, 3}, {6, 4}, {5, 5}, {10, 0}}
	profitThreshold := 20.0

	args := os.Args[1:]
	specifiedSkins := len(args) > 0

	start := time.Now()

	// Read JSON
	data, err := os.ReadFile("today.json")
	if err != nil {
		fmt.Println("Error reading file:", err)
		return
	}

	var all types.AllCollections
	if err := json.Unmarshal(data, &all); err != nil {
		panic(err)
	}

	// Precompute valid floats per skin
	validFloatsPerSkin := make(map[string][]float64)
	for _, collection := range all {
		for _, rarity := range collection {
			for skinName, skinData := range rarity {
				valid := []float64{}
				for _, f := range floats {
					if f >= skinData.WearRange[0] && f <= skinData.WearRange[1] {
						valid = append(valid, f)
					}
				}
				validFloatsPerSkin[skinName] = valid
			}
		}
	}

	var outcomes []types.Outcome
	var mu sync.Mutex
	var wg sync.WaitGroup

	for collectionName, collection := range all {
		wg.Add(1)
		go func(collectionName string, collection types.Collection) {
			defer wg.Done()

			tradeupSimLocalCache := types.LocalCache{
				LocalOutputSkinCache: make(map[types.OutputSkinKey]types.OutputSkin),
			}

			tenXChecked := make(map[string]bool)

			for rarityName, rarity := range collection {
				if rarityName == "Covert" {
					continue
				}

				for skinName, skinData := range rarity {

					if specifiedSkins {
						skip := true
						for _, s := range args {
							if s == skinName {
								skip = false
								break
							}
						}
						if skip {
							continue
						}
					}

					stattrak := strings.HasPrefix(skinName, "Stat")
					validFloats := validFloatsPerSkin[skinName]
					skinWearMin := skinData.WearRange[0]
					skinWearMax := skinData.WearRange[1]

					for _, flo := range validFloats {
						if flo < skinWearMin || flo > skinWearMax {
							continue
						}

						if flo == skinWearMin {
							if skinWearMin == 0.06 {
								flo = 0.065
							} else {
								flo += 0.02
							}
						}

						for collectionName2 := range all {
							rarity2, ok := all[collectionName2][rarityName]
							if !ok {
								continue
							}

							for skinName2, skinData2 := range rarity2 {
								if skinName == skinName2 {
									continue
								}

								if stattrak && !strings.HasPrefix(skinName2, "Stat") {
									continue
								}
								if !stattrak && strings.HasPrefix(skinName2, "Stat") {
									continue
								}

								validFloats2 := validFloatsPerSkin[skinName2]
								skin2WearMin := skinData2.WearRange[0]
								skin2WearMax := skinData2.WearRange[1]

								for _, flo2 := range validFloats2 {
									if flo2 < skin2WearMin || flo2 > skin2WearMax {
										continue
									}
									if flo2 == skin2WearMin {
										if skin2WearMin == 0.06 {
											flo2 = 0.065
										} else {
											flo2 += 0.02
										}
									}

									for _, combination := range combinationsToCheck {
										if combination[0] == 10 {
											key := collectionName + "|" + rarityName + "|" + skinName
											if _, ok := tenXChecked[key]; ok {
												continue
											}
											tenXChecked[key] = true
										}

										if skinData.Prices[tu.WearFloatToStr(flo)] == 0 || skinData2.Prices[tu.WearFloatToStr(flo2)] == 0 {
											continue
										}

										// Build inputItems slice
										inputItems := make([]types.InputSkin, 0, 2)
										inputItems = append(inputItems, types.InputSkin{
											Name: skinName, Wear: flo, Rarity: rarityName, Collection: collectionName, Quantity: combination[0],
										})
										if combination[1] != 0 {
											inputItems = append(inputItems, types.InputSkin{
												Name: skinName2, Wear: flo2, Rarity: rarityName, Collection: collectionName2, Quantity: combination[1],
											})
										}

										tradeupResult, _, err := tu.SimulateTradeup(all, tradeupSimLocalCache, inputItems, stattrak)
										if err != "" {
											continue
										}

										if tradeupResult.Metrics.ProfitabilityFees > profitThreshold {
											outItems := make([]struct {
												Name       string
												Wear       float64
												Quantity   int
												Rarity     string
												Price      float64
												Collection string
												Chance     float64
											}, 0, len(tradeupResult.Skins))

											for _, out := range tradeupResult.Skins {
												outItems = append(outItems, struct {
													Name       string
													Wear       float64
													Quantity   int
													Rarity     string
													Price      float64
													Collection string
													Chance     float64
												}{
													Name: out.Name,
													Wear: out.Wear,
													Quantity: 1,
													Rarity: out.Rarity,
													Price: out.Price,
													Collection: out.Collection,
													Chance: roundFloat(out.Chance, 4),
												})
											}

											outcome := struct {
												InputItems  []types.InputSkin
												OutputItems []struct {
													Name       string
													Wear       float64
													Quantity   int
													Rarity     string
													Price      float64
													Collection string
													Chance     float64
												}
												Metrics  types.OutputMetrics
												Stattrak bool
											}{
												InputItems: inputItems,
												OutputItems: outItems,
												Metrics: tradeupResult.Metrics,
												Stattrak: stattrak,
											}

											mu.Lock()
											outcomes = append(outcomes, outcome)
											mu.Unlock()
										}
									}
								}
							}
						}
					}
				}
			}
		}(collectionName, collection)
	}

	wg.Wait()

	fmt.Println("Outcomes:", len(outcomes))

	// Write JSON to file
	jsonBytes, err := json.MarshalIndent(outcomes, "", "  ")
	if err != nil {
		panic(err)
	}

	if err := os.WriteFile("result.json", jsonBytes, 0644); err != nil {
		fmt.Println("Error writing JSON to file:", err)
		return
	}

	fmt.Println("Done! Results written to result.json")
	fmt.Println("Time taken:", time.Since(start))
}

func contains(arr []string, s string) bool {
	for _, v := range arr {
		if v == s {
			return true
		}
	}
	return false
}
