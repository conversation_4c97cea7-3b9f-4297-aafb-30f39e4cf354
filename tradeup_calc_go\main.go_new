package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"runtime/pprof"
	"strings"
	"sync"

	tu "tradeup_calc/tradeup"
	"tradeup_calc/types"
)

func main() {
	// --- Start CPU profiling ---
	cpuProfile, err := os.Create("cpu.pprof")
	if err != nil {
		log.Fatal("could not create CPU profile: ", err)
	}
	defer cpuProfile.Close()

	if err := pprof.StartCPUProfile(cpuProfile); err != nil {
		log.Fatal("could not start CPU profile: ", err)
	}
	defer pprof.StopCPUProfile()
	// ---------------------------

	// Example data - populate these with actual values
	var data types.AllCollections
	var wears = [5]float64{0.06, 0.11, 0.26, 0.41, 0.60}
	var combinationsToCheck = [][2]int{{9, 1}, {8, 2}, {7, 3}, {6, 4}, {5, 5}, {10, 0}}

	// Load data from today.json
	dataFile, err := os.ReadFile("today.json")
	if err != nil {
		log.Fatal("Error reading file:", err)
	}

	if err := json.Unmarshal(dataFile, &data); err != nil {
		log.Fatal("Error unmarshaling JSON:", err)
	}

	// Open result.json for streaming
	file, err := os.Create("result.json")
	if err != nil {
		log.Fatal(err)
	}
	defer file.Close()

	file.Write([]byte("[")) // start JSON array

	var wg sync.WaitGroup
	var mu sync.Mutex
	first := true

	validFloatsPerSkin := make(map[string][]float64, 2000)
	validFloats := make([]float64, 2000)
	for collectionName, collection := range data {
		for rarityName, rarity := range collection {
			for skinName, skinData := range rarity {
				validFloats = validFloats[:0] // reset slice for each skin
				for _, f := range wears {
					if f >= skinData.WearRange[0] && f <= skinData.WearRange[1] {
						validFloats = append(validFloats, f)
					}
				}
				key := collectionName + "|" + rarityName + "|" + skinName
				validFloatsPerSkin[key] = validFloats
			}
		}
	}

	for collectionName, collection := range data {
		for rarityName, rarity := range collection {
			if rarityName == "Covert" {
				continue
			}

			for skinName := range rarity {
				wg.Add(1)
				go func(collectionName, rarityName, skinName string) {
					defer wg.Done()
					localCache := types.LocalCache{}
					var localOutcomes []types.Outcome
					var inputSkins [10]types.InputSkin

					var stattrak bool
					if strings.HasPrefix(skinName, "Stat") {
						stattrak = true
					} else {
						stattrak = false
					}

					// Nested loops to generate tradeups
					for _, wear := range validFloatsPerSkin[collectionName+"|"+rarityName+"|"+skinName] {
						for collectionName2, collection2 := range data {
							for rarityName2, rarity2 := range collection2 {
								if rarityName2 == "Covert" {
									continue
								}

								for skinName2 := range rarity2 {
									if skinName == skinName2 || rarityName != rarityName2 {
										continue
									}
									if (stattrak && !strings.HasPrefix(skinName2, "Stat")) ||
										(!stattrak && strings.HasPrefix(skinName2, "Stat")) {
										continue
									}

									for _, wear2 := range validFloatsPerSkin[collectionName2+"|"+rarityName2+"|"+skinName2] {
										for _, combination := range combinationsToCheck {
											for i := 0; i < combination[0]; i++ {
												inputSkins[i] = types.InputSkin{
													Name:       skinName,
													Wear:       wear,
													Rarity:     rarityName,
													Collection: collectionName,
												}
											}
											for i := combination[0]; i < 10; i++ {
												inputSkins[i] = types.InputSkin{
													Name:       skinName2,
													Wear:       wear2,
													Rarity:     rarityName2,
													Collection: collectionName2,
												}
											}

											tradeupResult, updatedCache, errorMsg := tu.SimulateTradeup(data, localCache, inputSkins, stattrak)
											if errorMsg != "" {
												continue
											}
											localCache = updatedCache
											if tradeupResult.Metrics.ProfitabilityFees < 110 {
												continue
											}

											localOutcomes = append(localOutcomes, types.Outcome{
												InputItems:  inputSkins,
												TradeupData: tradeupResult,
											})
										}
									}
								}
							}
						}
					}

					// Write outcomes directly to JSON
					if len(localOutcomes) == 0 {
						return
					}

					mu.Lock()
					defer mu.Unlock()
					for i, outcome := range localOutcomes {
						if !first || i > 0 {
							file.Write([]byte(",\n"))
						} else {
							first = false
						}

						var buf strings.Builder
						encoder := json.NewEncoder(&buf)
						encoder.SetIndent("  ", "  ") // pretty-print with two-space indent
						if err := encoder.Encode(outcome); err != nil {
							log.Println("Error encoding outcome:", err)
							continue
						}

						// Remove trailing newline added by Encode
						encoded := strings.TrimRight(buf.String(), "\n")
						file.Write([]byte(encoded))
					}
				}(collectionName, rarityName, skinName)
			}
		}
	}

	wg.Wait()

	file.Write([]byte("]")) // end JSON array
	fmt.Println("Finished writing result.json")
}
